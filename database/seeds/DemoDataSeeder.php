<?php

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Product;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create roles and permissions
        $this->createRolesAndPermissions();
        
        // Create admin user
        $this->createAdminUser();
        
        // Create basic business settings
        $this->createBusinessSettings();
        
        // Create demo categories
        $this->createDemoCategories();
        
        // Create demo brands
        $this->createDemoBrands();
        
        // Create demo products
        $this->createDemoProducts();
        
        // Create demo customers
        $this->createDemoCustomers();
    }
    
    private function createRolesAndPermissions()
    {
        // Create Super Admin role
        $superAdminRole = Role::firstOrCreate(['name' => 'Super Admin']);
        
        // Create basic permissions
        $permissions = [
            'admin_dashboard',
            'manage_products',
            'manage_categories',
            'manage_brands',
            'manage_orders',
            'manage_users',
            'manage_settings'
        ];
        
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }
        
        // Assign all permissions to Super Admin
        $superAdminRole->syncPermissions(Permission::all());
    }
    
    private function createAdminUser()
    {
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('admin123'),
                'user_type' => 'admin',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]
        );
        
        $admin->assignRole('Super Admin');
    }
    
    private function createBusinessSettings()
    {
        $settings = [
            ['type' => 'site_name', 'value' => 'Active eCommerce CMS'],
            ['type' => 'site_motto', 'value' => 'Your One-Stop Shop'],
            ['type' => 'site_icon', 'value' => ''],
            ['type' => 'site_logo', 'value' => ''],
            ['type' => 'currency_code', 'value' => 'USD'],
            ['type' => 'currency_symbol', 'value' => '$'],
            ['type' => 'system_default_currency', 'value' => '1'],
            ['type' => 'current_version', 'value' => '6.0'],
            ['type' => 'decimal_separator', 'value' => '.'],
            ['type' => 'thousands_separator', 'value' => ',']
        ];
        
        foreach ($settings as $setting) {
            BusinessSetting::firstOrCreate(
                ['type' => $setting['type']],
                ['value' => $setting['value']]
            );
        }
    }
    
    private function createDemoCategories()
    {
        $categories = [
            ['name' => 'Electronics', 'slug' => 'electronics', 'icon' => 'fas fa-laptop'],
            ['name' => 'Fashion', 'slug' => 'fashion', 'icon' => 'fas fa-tshirt'],
            ['name' => 'Home & Garden', 'slug' => 'home-garden', 'icon' => 'fas fa-home'],
            ['name' => 'Sports', 'slug' => 'sports', 'icon' => 'fas fa-football-ball'],
            ['name' => 'Books', 'slug' => 'books', 'icon' => 'fas fa-book'],
            ['name' => 'Health & Beauty', 'slug' => 'health-beauty', 'icon' => 'fas fa-heart'],
            ['name' => 'Toys & Games', 'slug' => 'toys-games', 'icon' => 'fas fa-gamepad'],
            ['name' => 'Automotive', 'slug' => 'automotive', 'icon' => 'fas fa-car']
        ];
        
        foreach ($categories as $index => $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                [
                    'name' => $categoryData['name'],
                    'parent_id' => 0,
                    'level' => 0,
                    'order_level' => $index + 1,
                    'commision_rate' => 0,
                    'banner' => '',
                    'icon' => $categoryData['icon'],
                    'featured' => 1,
                    'top' => 1,
                    'digital' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
    }
    
    private function createDemoBrands()
    {
        $brands = [
            ['name' => 'Apple', 'slug' => 'apple'],
            ['name' => 'Samsung', 'slug' => 'samsung'],
            ['name' => 'Nike', 'slug' => 'nike'],
            ['name' => 'Adidas', 'slug' => 'adidas'],
            ['name' => 'Sony', 'slug' => 'sony'],
            ['name' => 'LG', 'slug' => 'lg'],
            ['name' => 'Dell', 'slug' => 'dell'],
            ['name' => 'HP', 'slug' => 'hp']
        ];
        
        foreach ($brands as $brandData) {
            Brand::firstOrCreate(
                ['slug' => $brandData['slug']],
                [
                    'name' => $brandData['name'],
                    'logo' => '',
                    'top' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
    }
    
    private function createDemoProducts()
    {
        $products = [
            [
                'name' => 'iPhone 14 Pro',
                'slug' => 'iphone-14-pro',
                'category_id' => 1,
                'brand_id' => 1,
                'unit_price' => 999.99,
                'description' => 'Latest iPhone with advanced features'
            ],
            [
                'name' => 'Samsung Galaxy S23',
                'slug' => 'samsung-galaxy-s23',
                'category_id' => 1,
                'brand_id' => 2,
                'unit_price' => 899.99,
                'description' => 'Powerful Android smartphone'
            ],
            [
                'name' => 'Nike Air Max',
                'slug' => 'nike-air-max',
                'category_id' => 2,
                'brand_id' => 3,
                'unit_price' => 129.99,
                'description' => 'Comfortable running shoes'
            ],
            [
                'name' => 'Adidas Ultraboost',
                'slug' => 'adidas-ultraboost',
                'category_id' => 2,
                'brand_id' => 4,
                'unit_price' => 149.99,
                'description' => 'Premium athletic footwear'
            ]
        ];
        
        foreach ($products as $productData) {
            Product::firstOrCreate(
                ['slug' => $productData['slug']],
                [
                    'name' => $productData['name'],
                    'added_by' => 'admin',
                    'user_id' => 1,
                    'category_id' => $productData['category_id'],
                    'brand_id' => $productData['brand_id'],
                    'photos' => '',
                    'thumbnail_img' => '',
                    'video_provider' => 'youtube',
                    'video_link' => '',
                    'tags' => '',
                    'description' => $productData['description'],
                    'unit_price' => $productData['unit_price'],
                    'purchase_price' => $productData['unit_price'] * 0.7,
                    'variant_product' => 0,
                    'attributes' => '',
                    'choice_options' => '',
                    'colors' => '',
                    'variations' => '',
                    'todays_deal' => 0,
                    'published' => 1,
                    'approved' => 1,
                    'featured' => 1,
                    'current_stock' => 100,
                    'unit' => 'pc',
                    'min_qty' => 1,
                    'discount' => 0,
                    'discount_type' => 'amount',
                    'tax' => 0,
                    'tax_type' => 'percent',
                    'shipping_type' => 'free',
                    'shipping_cost' => 0,
                    'num_of_sale' => 0,
                    'meta_title' => $productData['name'],
                    'meta_description' => $productData['description'],
                    'meta_img' => '',
                    'pdf' => '',
                    'slug' => $productData['slug'],
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
    }
    
    private function createDemoCustomers()
    {
        $customers = [
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+1234567890'
            ],
            [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '+1234567891'
            ],
            [
                'name' => 'Mike Johnson',
                'email' => '<EMAIL>',
                'phone' => '+1234567892'
            ]
        ];
        
        foreach ($customers as $customerData) {
            User::firstOrCreate(
                ['email' => $customerData['email']],
                [
                    'name' => $customerData['name'],
                    'user_type' => 'customer',
                    'phone' => $customerData['phone'],
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
    }
}
