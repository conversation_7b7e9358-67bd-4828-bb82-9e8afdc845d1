# Active eCommerce CMS - Complete Setup Guide

## 🎯 Overview
This guide will help you set up Active eCommerce CMS locally on both Mac and Windows with full admin access and demo data. Choose the setup method that works best for your system.

---

## 🚀 Quick Start (Recommended)

### For Mac Users
```bash
# 1. Install dependencies
brew install php@8.3 mysql composer node

# 2. Clone/Navigate to project
cd /path/to/active-ecommerce-cms-main

# 3. Setup environment
cp .env.example .env
composer install --no-dev --optimize-autoloader

# 4. Create database and import data
mysql -u root -e "CREATE DATABASE active_ecommerce;"
mysql -u root active_ecommerce < shop.sql

# 5. Generate key and seed demo data
php artisan key:generate
php artisan db:seed

# 6. Start server
php artisan serve
```

### For Windows Users (XAMPP)
```cmd
# 1. Download and install XAMPP from https://www.apachefriends.org/
# 2. Start Apache and MySQL from XAMPP Control Panel
# 3. Open Command Prompt in project directory

# Setup environment
copy .env.example .env
composer install --no-dev --optimize-autoloader

# Create database and import data
mysql -u root -e "CREATE DATABASE active_ecommerce;"
mysql -u root active_ecommerce < shop.sql

# Generate key and seed demo data
php artisan key:generate
php artisan db:seed

# Start server
php artisan serve
```

### For Windows Users (Without XAMPP)
```cmd
# 1. Install PHP 8.3+ from https://windows.php.net/download/
# 2. Install MySQL from https://dev.mysql.com/downloads/installer/
# 3. Install Composer from https://getcomposer.org/download/
# 4. Add PHP to your PATH environment variable

# Then follow the same commands as XAMPP method above
```

**🎉 Access Your Application:**
- **Frontend:** http://127.0.0.1:8000
- **Admin Panel:** http://127.0.0.1:8000/admin
- **Login:** <EMAIL> / admin123

---

## 📋 Prerequisites

### System Requirements
- **PHP:** 8.2+ (Required)
- **MySQL:** 8.0+ or MariaDB 10.3+
- **Composer:** Latest version
- **Node.js:** 14+ (for frontend assets - optional)
- **Web Server:** Apache with mod_rewrite (or use Laravel's built-in server)

### Required PHP Extensions
- BCMath PHP Extension
- Ctype PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension
- ZipArchive Extension

---

## 🍎 Mac Setup Guide

### Step 1: Install Homebrew (if not already installed)
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

### Step 2: Install PHP 8.3
```bash
# Install PHP 8.3
brew install php@8.3

# Add PHP to PATH
echo 'export PATH="/opt/homebrew/opt/php@8.3/bin:$PATH"' >> ~/.zshrc
echo 'export PATH="/opt/homebrew/opt/php@8.3/sbin:$PATH"' >> ~/.zshrc

# Reload shell configuration
source ~/.zshrc

# Verify PHP installation
php --version
```

### Step 3: Install Composer
```bash
# Download and install Composer
curl -sS https://getcomposer.org/installer | php

# Move to global location
sudo mv composer.phar /usr/local/bin/composer

# Verify installation
composer --version
```

### Step 4: Install MySQL
```bash
# Install MySQL
brew install mysql

# Start MySQL service
brew services start mysql

# Secure MySQL installation (optional)
mysql_secure_installation
```

### Step 5: Install Node.js (for frontend assets)
```bash
# Install Node.js
brew install node

# Verify installation
node --version
npm --version
```

---

## 🪟 Windows Setup Guide

### Option 1: Using XAMPP (Recommended for Beginners)

#### Step 1: Install XAMPP
1. Download XAMPP from: https://www.apachefriends.org/download.html
2. Install XAMPP with PHP 8.2+ and MySQL
3. Start Apache and MySQL from XAMPP Control Panel

#### Step 2: Install Composer
1. Download Composer from: https://getcomposer.org/download/
2. Run the installer and follow the setup wizard
3. Verify installation in Command Prompt:
```cmd
composer --version
```

#### Step 3: Configure PHP Extensions
1. Open XAMPP Control Panel
2. Click "Config" next to Apache
3. Select "PHP (php.ini)"
4. Ensure all required extensions are enabled:
```ini
extension=bcmath
extension=ctype
extension=json
extension=mbstring
extension=openssl
extension=pdo_mysql
extension=tokenizer
extension=xml
extension=zip
```

### Option 2: Manual Installation (Without XAMPP)

#### Step 1: Install PHP
1. Download PHP 8.3+ from: https://windows.php.net/download/
2. Extract to `C:\php`
3. Add `C:\php` to your PATH environment variable
4. Copy `php.ini-development` to `php.ini`
5. Enable required extensions in `php.ini`

#### Step 2: Install MySQL
1. Download MySQL from: https://dev.mysql.com/downloads/installer/
2. Install MySQL Server and MySQL Workbench
3. Set root password during installation
4. Add MySQL bin directory to PATH

#### Step 3: Install Composer
1. Download and install from: https://getcomposer.org/download/
2. Verify installation:
```cmd
composer --version
```

#### Step 4: Install Node.js (Optional)
1. Download Node.js from: https://nodejs.org/
2. Install the LTS version
3. Verify installation:
```cmd
node --version
npm --version
```

---

## 🚀 Application Setup (Both Mac & Windows)

### Step 1: Navigate to Project Directory
```bash
# Mac/Linux
cd /path/to/active-ecommerce-cms-main

# Windows
cd C:\path\to\active-ecommerce-cms-main
```

### Step 2: Setup Environment
```bash
# Copy environment file
cp .env.example .env    # Mac/Linux
copy .env.example .env  # Windows

# The .env file is already configured for local development
# Default database settings:
# DB_HOST=127.0.0.1
# DB_DATABASE=active_ecommerce
# DB_USERNAME=root
# DB_PASSWORD=
```

### Step 3: Install PHP Dependencies
```bash
composer install --no-dev --optimize-autoloader
```

### Step 4: Create Database
```bash
# For both Mac and Windows
mysql -u root -e "CREATE DATABASE IF NOT EXISTS active_ecommerce;"

# If you get access denied, try with password:
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS active_ecommerce;"
```

### Step 5: Import Database Structure
```bash
# Import the main database structure
mysql -u root active_ecommerce < shop.sql

# If you have a password:
mysql -u root -p active_ecommerce < shop.sql
```

### Step 6: Generate Application Key
```bash
php artisan key:generate
```

### Step 7: Create Demo Data
```bash
# This will create sample products, categories, brands, and users
php artisan db:seed
```

### Step 8: Clear Caches
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

### Step 9: Create Public Directory Structure
```bash
# Create public directory if it doesn't exist
mkdir public                    # Mac/Linux
mkdir public                    # Windows

# Copy index.php to public (if needed)
cp index.php public/index.php   # Mac/Linux
copy index.php public\index.php # Windows
```

---

## 👑 Admin User (Already Created)

The demo data seeder automatically creates a Super Admin user with the following credentials:

**Login Credentials:**
- **Email:** <EMAIL>
- **Password:** admin123
- **Role:** Super Admin

### Verify Admin User (Optional)
```bash
php artisan tinker --execute="
\$user = App\Models\User::where('email', '<EMAIL>')->first();
if (\$user) {
    echo 'Admin user exists: ' . \$user->name . ' (' . \$user->email . ')' . PHP_EOL;
    echo 'User type: ' . \$user->user_type . PHP_EOL;
    echo 'Roles: ' . \$user->getRoleNames()->implode(', ') . PHP_EOL;
} else {
    echo 'Admin user not found!' . PHP_EOL;
}
"
```

### Create Additional Admin User (If Needed)
```bash
php artisan tinker --execute="
\$user = new App\Models\User();
\$user->name = 'Your Name';
\$user->email = '<EMAIL>';
\$user->password = bcrypt('your-password');
\$user->user_type = 'admin';
\$user->email_verified_at = now();
\$user->save();
\$user->assignRole('Super Admin');
echo 'New admin user created successfully!';
"
```

---

## 🚀 Start the Application

### Start Laravel Development Server
```bash
php artisan serve
```

### Access Your Application
- **Frontend:** http://127.0.0.1:8000
- **Admin Panel:** http://127.0.0.1:8000/admin

### Login Credentials
- **Email:** <EMAIL>
- **Password:** admin123

---

## 🎨 Build Frontend Assets (Optional)

If you want to customize the frontend styling:

```bash
# Install Node.js dependencies (if not already done)
npm install

# Build production assets
npm run production

# Or build development assets with watch
npm run dev

# For continuous development
npm run watch
```

---

## 🆕 Fresh Installation Options

### Option 1: Complete Fresh Start (Recommended)
If you want to start completely fresh without any demo data:

#### Step 1: Drop and Recreate Database
```bash
# Drop existing database
mysql -u root -e "DROP DATABASE IF EXISTS active_ecommerce;"

# Create fresh database
mysql -u root -e "CREATE DATABASE active_ecommerce;"
```

#### Step 2: Import Only Database Structure
```bash
# Import only the main database structure (no demo data)
mysql -u root active_ecommerce < shop.sql

# DO NOT import demo.sql - this keeps it clean
```

#### Step 3: Create Fresh Admin User
```bash
php artisan tinker --execute="
\$user = new App\Models\User();
\$user->name = 'Admin';
\$user->email = '<EMAIL>';
\$user->password = bcrypt('admin123');
\$user->user_type = 'admin';
\$user->email_verified_at = now();
\$user->save();
echo 'Fresh admin user created successfully!';
"
```

#### Step 4: Assign Admin Role
```bash
php artisan tinker --execute="
\$user = App\Models\User::where('email', '<EMAIL>')->first();
\$user->assignRole('Super Admin');
echo 'Admin role assigned successfully!';
"
```

### Option 2: Use Web Installation (Alternative Method)
You can also use the built-in web installer for a completely fresh setup:

#### Step 1: Access Installation
1. Go to: http://127.0.0.1:8000
2. You should see the installation wizard

#### Step 2: Follow Installation Steps
1. **Step 1:** File Permission Check
2. **Step 2:** Database Configuration
   - Database Host: `localhost`
   - Database Name: `active_ecommerce`
   - Username: `root`
   - Password: (leave empty)
3. **Step 3:** Import SQL
   - Choose "Install without Demo Data" for fresh installation
4. **Step 4:** Shop Information
   - Fill in your shop details
   - Create admin account
5. **Step 5:** Complete Installation

### Option 3: Remove Demo Data from Existing Installation
If you already have demo data and want to remove it:

#### Step 1: Clear Demo Data
```bash
php artisan tinker --execute="
// Remove demo products
App\Models\Product::truncate();

// Remove demo categories (keep main structure)
App\Models\Category::where('id', '>', 3)->delete();

// Remove demo brands
App\Models\Brand::truncate();

// Remove demo orders
App\Models\Order::truncate();

// Remove demo customers (keep admin)
App\Models\User::where('user_type', 'customer')->delete();

echo 'Demo data cleared successfully!';
"
```

#### Step 2: Reset to Fresh State
```bash
# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Regenerate application key
php artisan key:generate
```

### What You Get with Fresh Installation:
- ✅ **Clean Database:** No demo products, categories, or orders
- ✅ **Admin User:** Only your admin account
- ✅ **Basic Structure:** Essential tables and settings
- ✅ **Custom Setup:** You can configure everything from scratch
- ✅ **No Demo Content:** No sample products or categories

### What You Need to Set Up Manually:
- 🛍️ **Products:** Add your own products
- 📂 **Categories:** Create your product categories
- 🏪 **Store Settings:** Configure store name, logo, contact info
- 💳 **Payment Methods:** Set up payment gateways
- 📧 **Email Settings:** Configure email notifications
- 🎨 **Theme Customization:** Customize the look and feel

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. "Could not open input file: artisan"
**Solution:** Make sure you're in the correct directory and the artisan file exists.
```bash
# Check if you're in the right directory
ls -la artisan

# Mac/Linux: Make artisan executable
chmod +x artisan

# Windows: Use php artisan instead of just artisan
php artisan --version
```

#### 2. "Class not found" errors
**Solution:** Clear caches and reinstall dependencies.
```bash
composer dump-autoload
php artisan config:clear
php artisan cache:clear
```

#### 3. Database connection errors
**Solution:** Check database credentials and MySQL service.
```bash
# Check if MySQL is running
# Mac:
brew services list | grep mysql

# Windows (XAMPP):
# Check XAMPP Control Panel - MySQL should be green/running

# Test database connection
mysql -u root -e "SELECT 1;"

# Check .env file settings
cat .env | grep DB_
```

#### 4. Permission errors (Mac/Linux)
**Solution:** Set proper permissions:
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
sudo chown -R $USER:$USER storage bootstrap/cache
```

#### 5. Permission errors (Windows)
**Solution:** Run Command Prompt as Administrator or check folder permissions.

#### 6. "The provided cwd does not exist" error
**Solution:** Create the public directory structure:
```bash
mkdir public
cp index.php public/index.php    # Mac/Linux
copy index.php public\index.php  # Windows
```

#### 7. Admin panel shows "uploaded files" instead of sidebar
**Solution:** The admin user should already have the Super Admin role, but if not:
```bash
php artisan tinker --execute="
\$user = App\Models\User::where('email', '<EMAIL>')->first();
\$user->assignRole('Super Admin');
echo 'Role assigned successfully!';
"
```

#### 8. MySQL "Access denied" error
**Solution:**
```bash
# Try with password
mysql -u root -p

# Or reset MySQL root password (XAMPP)
# In XAMPP, MySQL usually has no password by default

# For manual MySQL installation, reset password:
mysql -u root --skip-password
```

#### 9. Composer install fails
**Solution:**
```bash
# Clear composer cache
composer clear-cache

# Update composer
composer self-update

# Install with verbose output to see errors
composer install -vvv
```

#### 10. PHP extensions missing
**Solution:**
```bash
# Check installed extensions
php -m

# Mac (install missing extensions):
brew install php@8.3-bcmath php@8.3-mbstring

# Windows (enable in php.ini):
# Uncomment lines like: extension=bcmath
```

---

## 📊 Verify Installation

### Check Demo Data
```bash
# Check products
mysql -u root -e "USE active_ecommerce; SELECT COUNT(*) as total_products FROM products;"

# Check categories
mysql -u root -e "USE active_ecommerce; SELECT COUNT(*) as total_categories FROM categories;"

# Check brands
mysql -u root -e "USE active_ecommerce; SELECT COUNT(*) as total_brands FROM brands;"

# Check users
mysql -u root -e "USE active_ecommerce; SELECT COUNT(*) as total_users FROM users;"
```

### Expected Results
- **Products:** 4 demo products (iPhone 14 Pro, Samsung Galaxy S23, Nike Air Max, Adidas Ultraboost)
- **Categories:** 8 main categories (Electronics, Fashion, Home & Garden, Sports, Books, Health & Beauty, Toys & Games, Automotive)
- **Brands:** 8 brands (Apple, Samsung, Nike, Adidas, Sony, LG, Dell, HP)
- **Users:** 4 users (1 admin + 3 demo customers)

---

## 🎯 Admin Panel Features

Once logged in as Super Admin, you'll have access to:

### Dashboard
- Sales analytics
- Order statistics
- Product performance
- User management

### Product Management
- Add/Edit/Delete products
- Manage categories
- Handle inventory
- Set pricing

### Order Management
- Process orders
- Handle refunds
- Manage shipping
- Track deliveries

### User Management
- Manage customers
- Handle sellers
- Assign roles
- Control permissions

### System Settings
- Store configuration
- Payment gateways
- Email settings
- SEO optimization

---

## 🔄 Daily Operations

### Start the Server
```bash
cd /path/to/your/project/install
php artisan serve
```

### Stop the Server
Press `Ctrl+C` in the terminal

### Restart Services (if needed)
```bash
# Mac
brew services restart mysql

# Windows (XAMPP)
# Use XAMPP Control Panel to restart services
```

---

## 📱 Mobile App Integration

The system includes Flutter mobile app support:
- API endpoints are available at `/api`
- Postman collection included: `FlutterEcommerceAPI.postman_collection.json`
- Mobile app can connect to the same database

---

## 🚀 Production Deployment

### For Production Server:
1. Upload files to server's `public_html` directory
2. Create production database
3. Update `.env` with production settings
4. Run installation through web interface
5. Activate license with production domain

### Security Considerations:
- Change default admin password
- Use strong database passwords
- Enable HTTPS
- Regular backups
- Keep system updated

---

## 📞 Support

### Documentation
- Check the included PDF documentation
- Review the Postman API collection
- Explore the admin panel features

### Common Commands
```bash
# Clear all caches
php artisan optimize:clear

# Check system requirements
php artisan about

# Run migrations (if needed)
php artisan migrate

# Seed database (if needed)
php artisan db:seed
```

---

## ✅ Final Checklist

- [ ] PHP 8.2+ installed and configured
- [ ] MySQL running and accessible
- [ ] Composer dependencies installed
- [ ] Database created and imported with `shop.sql`
- [ ] Environment configured (`.env` file)
- [ ] Application key generated
- [ ] Demo data seeded successfully
- [ ] Public directory structure created
- [ ] Server running on port 8000
- [ ] Frontend accessible at http://127.0.0.1:8000
- [ ] Admin panel accessible at http://127.0.0.1:8000/admin
- [ ] Can <NAME_EMAIL> / admin123
- [ ] Demo products, categories, and brands visible

---

## 🎯 What You Get

### Demo Data Included:
- **4 Sample Products:** iPhone 14 Pro, Samsung Galaxy S23, Nike Air Max, Adidas Ultraboost
- **8 Categories:** Electronics, Fashion, Home & Garden, Sports, Books, Health & Beauty, Toys & Games, Automotive
- **8 Brands:** Apple, Samsung, Nike, Adidas, Sony, LG, Dell, HP
- **4 Users:** 1 Super Admin + 3 Demo Customers
- **Roles & Permissions:** Fully configured admin access

### Admin Panel Features:
- Dashboard with analytics
- Product management
- Category management
- Brand management
- User management
- Order management
- System settings
- Role-based permissions

---

## 🚀 Next Steps

1. **Customize Your Store:**
   - Update store name and logo in Admin → Settings
   - Add your own products and categories
   - Configure payment gateways
   - Set up email notifications

2. **Development:**
   - Modify frontend templates in `resources/views`
   - Customize styling with `npm run dev`
   - Add custom functionality

3. **Production Deployment:**
   - Upload to your web server
   - Update `.env` with production settings
   - Configure domain and SSL
   - Set up automated backups

---

**🎉 Congratulations! Your Active eCommerce CMS is now fully set up and ready to use!**

**Frontend:** http://127.0.0.1:8000
**Admin Panel:** http://127.0.0.1:8000/admin
**Login:** <EMAIL> / admin123

**To start the server:** `php artisan serve`